import os
import sys
from src.agent.agent import AgenticRAG  # your existing class
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import <PERSON><PERSON><PERSON><PERSON>, Agent, JobContext, llm
from livekit.plugins import langchain, deepgram, cartesia, silero,groq,speechify
from dotenv import load_dotenv

load_dotenv()
instructions=(
                "You are a sophisticated, multilingual voice AI assistant with advanced language detection and intelligent tool routing capabilities.\n\n"

                "🚨 **ABSOLUTE MANDATORY LANGUAGE RULE - NO EXCEPTIONS:**\n"
                "- **NEVER RESPOND IN ENGLISH** unless the user is speaking English\n"
                "- **ALWAYS RESPOND IN THE USER'S DETECTED LANGUAGE** from the very first word\n"
                "- **USE ENGLISH ALPHABET TRANSLITERATION** for all non-English languages\n"
                "- **MAINTAIN SAME LANGUAGE** throughout entire conversation\n\n"

                "**CRITICAL LANGUAGE HANDLING - ABSOLUTE PRIORITY:**\n"
                "1. **Language Detection:** User's language is automatically detected from their input.\n"
                "2. **MANDATORY Response Language:** You MUST respond in the SAME language as the user from the FIRST response.\n"
                "3. **Supported Languages - EXACT RESPONSE FORMAT:**\n"
                "   - **English:** Respond in natural English only\n"
                "   - **German:** Respond using English alphabet but German words (e.g., 'Hallo, ich kann Ihnen helfen')\n"
                "   - **French:** Respond using English alphabet but French words (e.g., 'Bonjour, je peux vous aider')\n"
                "   - **Hindi:** Respond using English alphabet transliteration (e.g., 'Namaste, main aapki madad kar sakta hun')\n"
                "   - **Tamil:** Respond using English alphabet transliteration (e.g., 'Vanakkam, naan ungalukku uthavi seiya mudiyum')\n"
                "   - **Telugu:** Respond using English alphabet transliteration (e.g., 'Namaskaram, nenu mee sahayam cheyagalanu')\n"
                "4. **ZERO English Mixing:** Never use English words in non-English responses.\n"
                "5. **Tool Response Language:** When using tools, format the final response in the user's language.\n"
                "6. **Language Context Processing:** When tools provide '[LANGUAGE: X]' markers, use that language for your response.\n\n"

                "**INTELLIGENT TOOL ROUTING - CRITICAL RULES:**\n"
                "You have access to TWO distinct tools:\n\n"

                "🔍 **vector_database_search** - Use for:\n"
                "- Technical documentation (electrical machines, transformers, motors)\n"
                "- Internal knowledge and stored documents\n"
                "- Domain-specific information\n"
                "- Company policies and procedures\n"
                "- Historical data and specifications\n\n"

                "🌐 **web_search** - Use for:\n"
                "- Current news and recent events\n"
                "- Real-time information (weather, stock prices)\n"
                "- Breaking news and latest developments\n"
                "- Recent product releases\n"
                "- When vector database doesn't have relevant information\n\n"

                "**TOOL SELECTION PRIORITY:**\n"
                "1. For technical/domain questions → Try vector_database_search FIRST\n"
                "2. For current events/real-time data → Use web_search DIRECTLY\n"
                "3. If vector search returns insufficient results → Use web_search as fallback\n"
                "4. For simple greetings/basic conversation → Respond directly without tools\n\n"

                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Hello, can you help?' → You respond: 'Hello, I can help you with that'\n"
                "User says 'Hallo, können Sie helfen?' → You respond: 'Hallo, ich kann Ihnen helfen'\n"
                "User says 'Bonjour, pouvez-vous aider?' → You respond: 'Bonjour, je peux vous aider'\n"
                "User says 'Namaste, madad kar sakte hain?' → You respond: 'Namaste, main aapki madad kar sakta hun'\n"
                "User says 'Vanakkam, help panna mudiyuma?' → You respond: 'Vanakkam, naan ungalukku uthavi seiya mudiyum'\n"
                "User says 'Namaskaram, sahayam cheyagalara?' → You respond: 'Namaskaram, nenu mee sahayam cheyagalanu'\n\n"

                "🚨 **CRITICAL RESPONSE RULES:**\n"
                "1. **FIRST RESPONSE RULE:** Your very first response MUST be in the user's language\n"
                "2. **TOOL RESPONSE RULE:** After using any tool, format your response in the user's language\n"
                "3. **NO ENGLISH FALLBACK:** Never fall back to English unless user is speaking English\n"
                "4. **TRANSLITERATION ONLY:** Use English alphabet but user's language words\n"
                "5. **CONSISTENCY RULE:** Same language from first response to last response\n"
                "6. **VOICE OPTIMIZATION:** Make responses sound natural when spoken aloud"
            ),
            

# Create your LangGraph workflow instance and tools
rag_agent = AgenticRAG()
language_detector = LanguageDetector()

# Global variables for session language tracking
session_language = None
session_language_config = None
session_language_instructions = None

def create_vector_database_tool():
    """
    Create a vector database search tool for internal knowledge retrieval.
    """
    # Initialize the vector database tool
    vector_tool = VectorDatabaseTool(rag_agent.llm)

    @llm.function_tool(
        name="vector_database_search",
        description="Search internal documents and knowledge base for technical information, specifications, and stored content"
    )
    async def vector_database_search(query: str) -> str:
        """
        Search internal vector database for technical documentation and stored knowledge.

        Use this tool for:
        - Technical questions (electrical machines, motors, generators, transformers)
        - Internal documentation and manuals
        - Domain-specific information
        - Company policies and procedures
        - Historical data and archived content
        - Previously stored research and documents

        Args:
            query: The user's question about internal knowledge or technical information

        Returns:
            Response from internal knowledge base
        """
        try:
            global session_language, session_language_config, session_language_instructions
            logging.info(f"Vector database tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")
                logging.info(f"Language type: {language_detector.get_language_type(lang_code)}")

            # Search vector database with language context
            result = vector_tool.search_documents(query, session_language)

            if result['is_relevant']:
                response = result['results']
                logging.info(f"Vector database found relevant results")

                # CRITICAL: Add mandatory language instruction for LLM
                if session_language != 'en':
                    lang_name = session_language_config['name']
                    sample_greeting = session_language_config['sample_phrases']['greeting']
                    response = f"[MANDATORY LANGUAGE: {lang_name} ({session_language}) - RESPOND ONLY IN {lang_name.upper()} USING ENGLISH ALPHABET TRANSLITERATION. EXAMPLE: '{sample_greeting}' - NO ENGLISH WORDS ALLOWED]\n\n{response}"
                else:
                    response = f"[LANGUAGE: English - Respond in natural English]\n\n{response}"
            else:
                # Provide language-appropriate "no results" message
                if session_language == 'hi':
                    response = "Mere paas is sawal ka jawaab mere knowledge base mein nahi hai. Kripaya web search try kariye current information ke liye."
                elif session_language == 'ta':
                    response = "Enakku indha kelvikku ennoda knowledge base la relevant information illa. Current information kkaaga web search try pannunga."
                elif session_language == 'te':
                    response = "Naa knowledge base lo ee prashnaku sambandhinchinavi levu. Current information kosam web search try cheyandi."
                elif session_language == 'de':
                    response = "Ich habe keine relevanten Informationen in meiner Wissensdatenbank für diese Anfrage. Bitte versuchen Sie die Websuche für aktuelle Informationen."
                elif session_language == 'fr':
                    response = "Je n'ai pas d'informations pertinentes dans ma base de connaissances pour cette requête. Veuillez essayer la recherche web pour des informations actuelles."
                else:
                    response = "I don't have relevant information in my knowledge base for this query. Please try the web search for current information."
                logging.info(f"Vector database did not find relevant results")

            return response

        except Exception as e:
            logging.error(f"Vector database tool error: {e}")
            return "I encountered an issue searching the knowledge base. Please try rephrasing your question."

    return vector_database_search

def create_web_search_tool():
    """
    Create a web search tool for current and real-time information.
    """
    # Initialize the web search tool
    web_tool = WebSearchTool()

    @llm.function_tool(
        name="web_search",
        description="Search the internet for current news, real-time information, and recent events"
    )
    async def web_search(query: str) -> str:
        """
        Search the web for current information, news, and real-time data.

        Use this tool for:
        - Current news and recent events
        - Real-time information (weather, stock prices)
        - Breaking news and latest developments
        - Recent product releases or announcements
        - Current market data and live information
        - When vector database doesn't have relevant information

        Args:
            query: The user's question about current events or real-time information

        Returns:
            Current information from web search
        """
        try:
            global session_language, session_language_config, session_language_instructions
            logging.info(f"Web search tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")

            # Use session language for web search
            result = web_tool.search_web(query, session_language)

            response = result['results']
            logging.info(f"Web search completed with {result['result_count']} results")

            # CRITICAL: Add mandatory language instruction for LLM
            if session_language != 'en':
                lang_name = session_language_config['name']
                sample_greeting = session_language_config['sample_phrases']['greeting']
                response = f"[MANDATORY LANGUAGE: {lang_name} ({session_language}) - RESPOND ONLY IN {lang_name.upper()} USING ENGLISH ALPHABET TRANSLITERATION. EXAMPLE: '{sample_greeting}' - NO ENGLISH WORDS ALLOWED]\n\n{response}"
            else:
                response = f"[LANGUAGE: English - Respond in natural English]\n\n{response}"

            return response

        except Exception as e:
            logging.error(f"Web search tool error: {e}")
            return "I encountered an issue searching the web. Please try rephrasing your question."

    return web_search

def get_dynamic_language_instructions(lang_code: str, lang_config: dict) -> str:
    """
    Generate dynamic language instructions based on detected language.
    """
    if lang_code == 'en':
        return "\n**CURRENT SESSION LANGUAGE:** English - Respond in clear, natural English."

    lang_name = lang_config.get('name', 'Unknown')
    lang_type = language_detector.get_language_type(lang_code)

    if lang_type == 'indian':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Use English alphabet transliteration that sounds natural when spoken in {lang_name}.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Never use English words - always use {lang_name} equivalents in transliteration.
"""
    elif lang_type == 'european':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Respond in proper {lang_name} with correct grammar and native vocabulary.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Use authentic {lang_name} - no English mixing allowed.
"""
    else:
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**RESPONSE FORMAT:** Maintain natural pronunciation patterns in {lang_name}.
"""

async def entrypoint(ctx: JobContext):
    try:
        await ctx.connect()  # Agent joins inbound call room automatically

        # Create the separate tools
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
                detect_language=True,

            ),
             llm=groq.LLM(
                model="llama3-70b-8192",

            ),

            tts=speechify.TTS(
                model="simba-multilingual"),


        )

        # Create agent with both tools
        agent = Agent(
            llm=session.llm,
            instructions=instructions,
            tools=[vector_tool, web_tool]
        )

        await session.start(agent=agent, room=ctx.room)

        await session.generate_reply(instructions="Hello! How may I assist you today?")

    except Exception as e:
        logging.error(f"voice agent error: {e}")
        raise CustomException(e,sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
