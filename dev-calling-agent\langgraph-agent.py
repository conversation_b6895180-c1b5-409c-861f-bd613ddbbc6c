import os
import sys
from src.agent.agent import AgenticRAG  # your existing class
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import <PERSON><PERSON>ession, Agent, JobContext, llm
from livekit.plugins import langchain, deepgram, cartesia, silero,groq,speechify
from dotenv import load_dotenv

load_dotenv()
# Base instructions that will be dynamically updated
base_instructions = (
    "You are a sophisticated, multilingual voice AI assistant with advanced language detection and intelligent tool routing capabilities.\n\n"

    "**INTELLIGENT TOOL ROUTING - CRITICAL RULES:**\n"
    "You have access to TWO distinct tools:\n\n"

    "🔍 **vector_database_search** - Use for:\n"
    "- Technical documentation (electrical machines, transformers, motors)\n"
    "- Internal knowledge and stored documents\n"
    "- Domain-specific information\n"
    "- Company policies and procedures\n"
    "- Historical data and specifications\n\n"

    "🌐 **web_search** - Use for:\n"
    "- Current news and recent events\n"
    "- Real-time information (weather, stock prices)\n"
    "- Breaking news and latest developments\n"
    "- Recent product releases\n"
    "- When vector database doesn't have relevant information\n\n"

    "**TOOL SELECTION PRIORITY:**\n"
    "1. For technical/domain questions → Try vector_database_search FIRST\n"
    "2. For current events/real-time data → Use web_search DIRECTLY\n"
    "3. If vector search returns insufficient results → Use web_search as fallback\n"
    "4. For simple greetings/basic conversation → Respond directly without tools\n\n"
)

def get_dynamic_instructions(lang_code: str = None, lang_config: dict = None) -> str:
    """
    Generate dynamic instructions based on detected language.
    """
    if lang_code is None or lang_code == 'en':
        language_rules = (
            "🚨 **LANGUAGE RULE:**\n"
            "- Respond in clear, natural English\n"
            "- Keep responses conversational and concise for voice interaction\n\n"
        )
        examples = (
            "🎯 **RESPONSE EXAMPLES:**\n"
            "User says 'Hello, can you help?' → You respond: 'Hello, I can help you with that'\n"
            "User asks about weather → You respond: 'Let me check the current weather for you'\n\n"
        )
    else:
        lang_name = lang_config.get('name', 'Unknown') if lang_config else 'Unknown'
        sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '') if lang_config else ''

        language_rules = (
            f"🚨 **ABSOLUTE MANDATORY LANGUAGE RULE - NO EXCEPTIONS:**\n"
            f"- **NEVER RESPOND IN ENGLISH** - You MUST respond ONLY in {lang_name}\n"
            f"- **USE ENGLISH ALPHABET TRANSLITERATION** for {lang_name} words\n"
            f"- **ZERO ENGLISH MIXING** - Never use English words in your responses\n"
            f"- **MAINTAIN CONSISTENCY** - Same language throughout entire conversation\n"
            f"- **EXAMPLE FORMAT:** '{sample_greeting}'\n\n"
        )

        if lang_code == 'hi':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Namaste, madad kar sakte hain?' → You respond: 'Namaste, main aapki madad kar sakta hun'\n"
                "User asks about weather → You respond: 'Main aapke liye mausam ki jaankaari dekh raha hun'\n"
                "User says 'Dhanyawad' → You respond: 'Koi baat nahi, main yahan aapki madad ke liye hun'\n\n"
            )
        elif lang_code == 'ta':
            examples = (
                "� **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Vanakkam, help panna mudiyuma?' → You respond: 'Vanakkam, naan ungalukku uthavi seiya mudiyum'\n"
                "User asks about weather → You respond: 'Naan ungalukku weather information paakka poren'\n"
                "User says 'Nandri' → You respond: 'Paravala, naan inga ungalukku uthavi seiya irukken'\n\n"
            )
        elif lang_code == 'te':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Namaskaram, sahayam cheyagalara?' → You respond: 'Namaskaram, nenu mee sahayam cheyagalanu'\n"
                "User asks about weather → You respond: 'Nenu meeku weather information chustha'\n"
                "User says 'Dhanyawadalu' → You respond: 'Parledu, nenu ikkada mee sahayam kosam unnaanu'\n\n"
            )
        elif lang_code == 'de':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Hallo, können Sie helfen?' → You respond: 'Hallo, ich kann Ihnen helfen'\n"
                "User asks about weather → You respond: 'Ich schaue das Wetter für Sie nach'\n"
                "User says 'Danke' → You respond: 'Gern geschehen, ich bin hier um Ihnen zu helfen'\n\n"
            )
        elif lang_code == 'fr':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Bonjour, pouvez-vous aider?' → You respond: 'Bonjour, je peux vous aider'\n"
                "User asks about weather → You respond: 'Je vérifie la météo pour vous'\n"
                "User says 'Merci' → You respond: 'De rien, je suis là pour vous aider'\n\n"
            )
        else:
            examples = (
                f"🎯 **RESPONSE EXAMPLES for {lang_name}:**\n"
                f"Always respond in {lang_name} using appropriate transliteration\n"
                f"Example greeting: '{sample_greeting}'\n\n"
            )

    critical_rules = (
        "🚨 **CRITICAL RESPONSE RULES:**\n"
        "1. **IMMEDIATE LANGUAGE DETECTION:** Detect user's language from their first message\n"
        "2. **CONSISTENT LANGUAGE USE:** Use the same language throughout the entire conversation\n"
        "3. **TOOL RESPONSE FORMATTING:** When using tools, format your final response in the user's language\n"
        "4. **VOICE OPTIMIZATION:** Make responses sound natural when spoken aloud\n"
        "5. **LANGUAGE CONTEXT PROCESSING:** When tools provide '[LANGUAGE: X]' or '[MANDATORY LANGUAGE: X]' markers, follow those instructions exactly\n"
    )

    return base_instructions + language_rules + examples + critical_rules
            

# Create your LangGraph workflow instance and tools
rag_agent = AgenticRAG()
language_detector = LanguageDetector()

# Global variables for session language tracking
session_language = None
session_language_config = None
session_language_instructions = None
current_agent = None  # Reference to the current agent for instruction updates

def create_vector_database_tool():
    """
    Create a vector database search tool for internal knowledge retrieval.
    """
    # Initialize the vector database tool
    vector_tool = VectorDatabaseTool(rag_agent.llm)

    @llm.function_tool(
        name="vector_database_search",
        description="Search internal documents and knowledge base for technical information, specifications, and stored content"
    )
    async def vector_database_search(query: str) -> str:
        """
        Search internal vector database for technical documentation and stored knowledge.

        Use this tool for:
        - Technical questions (electrical machines, motors, generators, transformers)
        - Internal documentation and manuals
        - Domain-specific information
        - Company policies and procedures
        - Historical data and archived content
        - Previously stored research and documents

        Args:
            query: The user's question about internal knowledge or technical information

        Returns:
            Response from internal knowledge base
        """
        try:
            global session_language, session_language_config, session_language_instructions, current_agent
            logging.info(f"Vector database tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")
                logging.info(f"Language type: {language_detector.get_language_type(lang_code)}")

                # Update agent instructions if agent reference is available
                if current_agent and hasattr(current_agent, 'update_language_instructions'):
                    current_agent.update_language_instructions(lang_code, lang_config)

            # Search vector database with language context
            result = vector_tool.search_documents(query, session_language)

            if result['is_relevant']:
                response = result['results']
                logging.info(f"Vector database found relevant results")

                # CRITICAL: Add mandatory language instruction for LLM
                if session_language != 'en':
                    lang_name = session_language_config['name']
                    sample_greeting = session_language_config['sample_phrases']['greeting']
                    sample_error = session_language_config['sample_phrases']['error']
                    response = f"""[🚨 CRITICAL LANGUAGE ENFORCEMENT 🚨]
MANDATORY LANGUAGE: {lang_name} ({session_language})
ABSOLUTE RULE: RESPOND ONLY IN {lang_name.upper()} - NEVER USE ENGLISH WORDS
TRANSLITERATION: Use English alphabet for {lang_name} words
EXAMPLES:
- Greeting: '{sample_greeting}'
- Error: '{sample_error}'
VIOLATION PENALTY: Response will be rejected if English is used
[END LANGUAGE ENFORCEMENT]

{response}"""
                else:
                    response = f"[LANGUAGE: English - Respond in natural English]\n\n{response}"
            else:
                # Provide language-appropriate "no results" message
                if session_language == 'hi':
                    response = "Mere paas is sawal ka jawaab mere knowledge base mein nahi hai. Kripaya web search try kariye current information ke liye."
                elif session_language == 'ta':
                    response = "Enakku indha kelvikku ennoda knowledge base la relevant information illa. Current information kkaaga web search try pannunga."
                elif session_language == 'te':
                    response = "Naa knowledge base lo ee prashnaku sambandhinchinavi levu. Current information kosam web search try cheyandi."
                elif session_language == 'de':
                    response = "Ich habe keine relevanten Informationen in meiner Wissensdatenbank für diese Anfrage. Bitte versuchen Sie die Websuche für aktuelle Informationen."
                elif session_language == 'fr':
                    response = "Je n'ai pas d'informations pertinentes dans ma base de connaissances pour cette requête. Veuillez essayer la recherche web pour des informations actuelles."
                else:
                    response = "I don't have relevant information in my knowledge base for this query. Please try the web search for current information."
                logging.info(f"Vector database did not find relevant results")

            return response

        except Exception as e:
            logging.error(f"Vector database tool error: {e}")
            return "I encountered an issue searching the knowledge base. Please try rephrasing your question."

    return vector_database_search

def create_web_search_tool():
    """
    Create a web search tool for current and real-time information.
    """
    # Initialize the web search tool
    web_tool = WebSearchTool()

    @llm.function_tool(
        name="web_search",
        description="Search the internet for current news, real-time information, and recent events"
    )
    async def web_search(query: str) -> str:
        """
        Search the web for current information, news, and real-time data.

        Use this tool for:
        - Current news and recent events
        - Real-time information (weather, stock prices)
        - Breaking news and latest developments
        - Recent product releases or announcements
        - Current market data and live information
        - When vector database doesn't have relevant information

        Args:
            query: The user's question about current events or real-time information

        Returns:
            Current information from web search
        """
        try:
            global session_language, session_language_config, session_language_instructions, current_agent
            logging.info(f"Web search tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")

                # Update agent instructions if agent reference is available
                if current_agent and hasattr(current_agent, 'update_language_instructions'):
                    current_agent.update_language_instructions(lang_code, lang_config)

            # Use session language for web search
            result = web_tool.search_web(query, session_language)

            response = result['results']
            logging.info(f"Web search completed with {result['result_count']} results")

            # CRITICAL: Add mandatory language instruction for LLM
            if session_language != 'en':
                lang_name = session_language_config['name']
                sample_greeting = session_language_config['sample_phrases']['greeting']
                sample_error = session_language_config['sample_phrases']['error']
                response = f"""[🚨 CRITICAL LANGUAGE ENFORCEMENT 🚨]
MANDATORY LANGUAGE: {lang_name} ({session_language})
ABSOLUTE RULE: RESPOND ONLY IN {lang_name.upper()} - NEVER USE ENGLISH WORDS
TRANSLITERATION: Use English alphabet for {lang_name} words
EXAMPLES:
- Greeting: '{sample_greeting}'
- Error: '{sample_error}'
VIOLATION PENALTY: Response will be rejected if English is used
[END LANGUAGE ENFORCEMENT]

{response}"""
            else:
                response = f"[LANGUAGE: English - Respond in natural English]\n\n{response}"

            return response

        except Exception as e:
            logging.error(f"Web search tool error: {e}")
            # Return error message in user's language
            if session_language == 'hi':
                return "Mujhe web search mein kuch dikkat ho rahi hai. Kripaya apna sawal dusre tarike se puchiye."
            elif session_language == 'ta':
                return "Enakku web search la konjam problem irukku. Ungaloda kelviya vera vidhamaaga kekka mudiyuma?"
            elif session_language == 'te':
                return "Naaku web search lo konni samasyalu unnaayi. Dayachesi mee prashnanu vere vidhamgaa adagandi."
            elif session_language == 'de':
                return "Ich habe ein Problem bei der Websuche. Bitte formulieren Sie Ihre Frage anders."
            elif session_language == 'fr':
                return "J'ai rencontré un problème lors de la recherche web. Veuillez reformuler votre question."
            else:
                return "I encountered an issue searching the web. Please try rephrasing your question."

    return web_search

def get_dynamic_language_instructions(lang_code: str, lang_config: dict) -> str:
    """
    Generate dynamic language instructions based on detected language.
    """
    if lang_code == 'en':
        return "\n**CURRENT SESSION LANGUAGE:** English - Respond in clear, natural English."

    lang_name = lang_config.get('name', 'Unknown')
    lang_type = language_detector.get_language_type(lang_code)

    if lang_type == 'indian':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Use English alphabet transliteration that sounds natural when spoken in {lang_name}.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Never use English words - always use {lang_name} equivalents in transliteration.
"""
    elif lang_type == 'european':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Respond in proper {lang_name} with correct grammar and native vocabulary.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Use authentic {lang_name} - no English mixing allowed.
"""
    else:
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**RESPONSE FORMAT:** Maintain natural pronunciation patterns in {lang_name}.
"""

class LanguageAwareAgent(Agent):
    """
    Custom Agent that dynamically updates instructions based on detected language.
    """
    def __init__(self, llm, tools):
        # Start with default English instructions
        super().__init__(
            llm=llm,
            instructions=get_dynamic_instructions(),
            tools=tools
        )
        self.current_language = None
        self.language_config = None
        self.instructions_updated = False

    def update_language_instructions(self, lang_code: str, lang_config: dict):
        """Update agent instructions based on detected language."""
        if self.current_language != lang_code:
            self.current_language = lang_code
            self.language_config = lang_config

            # Generate new instructions for the detected language
            new_instructions = get_dynamic_instructions(lang_code, lang_config)

            # Update the agent's instructions
            self.instructions = new_instructions
            self.instructions_updated = True

            logging.info(f"Agent instructions updated for language: {lang_config.get('name', lang_code)} ({lang_code})")

    async def _handle_user_message(self, message):
        """Override to detect language from user messages and update instructions."""
        global session_language, session_language_config, session_language_instructions

        # Detect language from user message if not already detected
        if session_language is None and message.content:
            lang_code, lang_name, lang_config = language_detector.detect_language(message.content)
            session_language = lang_code
            session_language_config = lang_config
            session_language_instructions = language_detector.get_transliteration_instructions(lang_code)

            logging.info(f"Language detected from user message: {lang_name} ({lang_code})")

            # Update agent instructions
            self.update_language_instructions(lang_code, lang_config)

        # Call parent handler
        return await super()._handle_user_message(message)

    def _get_language_prefix(self) -> str:
        """Get language enforcement prefix for all responses."""
        if session_language and session_language != 'en' and session_language_config:
            lang_name = session_language_config['name']
            sample_greeting = session_language_config['sample_phrases']['greeting']
            return f"""🚨 CRITICAL: You MUST respond ONLY in {lang_name} using English alphabet transliteration.
EXAMPLE: '{sample_greeting}'
NEVER use English words. ALWAYS use {lang_name} equivalents.
"""
        return ""

    async def generate_response(self, message):
        """Override to add language enforcement to every response."""
        # Add language enforcement prefix
        language_prefix = self._get_language_prefix()

        if language_prefix:
            # Prepend language enforcement to the message
            enhanced_message = language_prefix + "\n\nUser message: " + str(message)
            return await super().generate_response(enhanced_message)
        else:
            return await super().generate_response(message)

async def entrypoint(ctx: JobContext):
    # Declare global variables at the beginning
    global session_language, session_language_config, session_language_instructions, current_agent

    try:
        await ctx.connect()  # Agent joins inbound call room automatically

        # Reset session language for new session
        session_language = None
        session_language_config = None
        session_language_instructions = None
        current_agent = None

        # Create the separate tools
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
                detect_language=True,
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
            ),
            tts=speechify.TTS(
                model="simba-multilingual"
            ),
        )

        # Create custom language-aware agent
        agent = LanguageAwareAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )

        # Set global reference for tools to access
        current_agent = agent

        await session.start(agent=agent, room=ctx.room)

        # Start with a multilingual greeting that will trigger language detection
        await session.generate_reply(instructions="Hello! Namaste! Vanakkam! How may I assist you today?")

    except Exception as e:
        logging.error(f"voice agent error: {e}")
        raise CustomException(e,sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
